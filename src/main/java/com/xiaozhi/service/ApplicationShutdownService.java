package com.xiaozhi.service;

import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.server.mqtt.MqttHandler;
import com.xiaozhi.communication.server.mqtt.udp.UdpServer;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 应用关闭钩子服务
 * 负责在应用关闭时优雅地清理所有资源，包括：
 * 1. 关闭所有活跃的会话（WebSocket、MQTT）
 * 2. 清理UDP连接和资源
 * 3. 断开MQTT连接
 * 4. 清理连接映射关系
 */
@Service
public class ApplicationShutdownService {
    
    private static final Logger logger = LoggerFactory.getLogger(ApplicationShutdownService.class);
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private MqttHandler mqttHandler;
    
    @Autowired
    private UdpServer udpServer;
    
    /**
     * 应用关闭时的资源清理
     * 按照依赖关系的逆序进行清理：
     * 1. 首先关闭所有会话（这会触发相关的清理工作）
     * 2. 然后关闭网络连接（MQTT、UDP）
     * 3. 最后清理映射关系
     */
    @PreDestroy
    public void shutdown() {
        logger.info("开始应用关闭清理流程...");
        
        try {
            // 1. 关闭所有活跃会话
            closeAllSessions();
            
            // 2. 等待一小段时间让会话清理完成
            Thread.sleep(1000);
            
            // 3. 清理MQTT连接映射
            clearMqttConnectionMappings();
            
            // 4. 确保UDP服务器完全停止
            ensureUdpServerStopped();
            
            logger.info("应用关闭清理流程完成");
            
        } catch (Exception e) {
            logger.error("应用关闭清理过程中发生错误", e);
        }
    }
    
    /**
     * 关闭所有活跃会话
     */
    private void closeAllSessions() {
        try {
            logger.info("开始关闭所有活跃会话...");
            int closedCount = sessionManager.closeAllSessions();
            logger.info("已关闭 {} 个活跃会话", closedCount);
        } catch (Exception e) {
            logger.error("关闭会话时发生错误", e);
        }
    }
    
    /**
     * 清理MQTT连接映射
     */
    private void clearMqttConnectionMappings() {
        try {
            logger.info("开始清理MQTT连接映射...");
            mqttHandler.clearAllConnectionMappings();
            logger.info("MQTT连接映射清理完成");
        } catch (Exception e) {
            logger.error("清理MQTT连接映射时发生错误", e);
        }
    }
    
    /**
     * 确保UDP服务器完全停止
     */
    private void ensureUdpServerStopped() {
        try {
            logger.info("确保UDP服务器完全停止...");
            
            // 等待UDP服务器停止，最多等待5秒
            int maxWaitSeconds = 5;
            int waitedSeconds = 0;
            
            while (udpServer.isRunning() && waitedSeconds < maxWaitSeconds) {
                Thread.sleep(1000);
                waitedSeconds++;
                logger.debug("等待UDP服务器停止... ({}/{}秒)", waitedSeconds, maxWaitSeconds);
            }
            
            if (udpServer.isRunning()) {
                logger.warn("UDP服务器在{}秒内未能完全停止", maxWaitSeconds);
            } else {
                logger.info("UDP服务器已完全停止");
            }
            
        } catch (Exception e) {
            logger.error("确保UDP服务器停止时发生错误", e);
        }
    }
}
