package com.xiaozhi.communication.server.mqtt;

import com.hivemq.client.mqtt.MqttClientState;
import com.hivemq.client.mqtt.datatypes.MqttQos;
import com.hivemq.client.mqtt.mqtt3.Mqtt3AsyncClient;
import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.communication.server.mqtt.udp.UdpInfo;
import com.xiaozhi.communication.server.mqtt.udp.UdpServer;
import com.xiaozhi.communication.server.mqtt.udp.crypto.CryptoUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * MQTT 会话实现
 * 基于 EMQX 消息队列，继承自 ChatSession
 * 实现基本的消息发送与接收，消息处理使用 MessageHandler
 * 使用 HiveMQ MQTT Client
 */
@Slf4j
public class MqttSession extends ChatSession {

    private final Mqtt3AsyncClient mqttClient;
    private final String deviceId;
    private final AtomicBoolean connected = new AtomicBoolean(false);

    private final int connectionId = new SecureRandom().nextInt(); // 32-bit
    private final ByteBuffer headerBuffer = ByteBuffer.allocate(16);  // 复用

    private final UdpServer udpServer;

    private final MqttHandler mqttHandler;

    private final String publishTopic;

    // UDP 信息
    @Getter
    private UdpInfo udpInfo;

    public MqttSession(String sessionId, String deviceId, String clientId, Mqtt3AsyncClient sharedMqttClient, UdpServer udpServer, MqttHandler mqttHandler) {
        super(sessionId);
        this.deviceId = deviceId;
        this.mqttClient = sharedMqttClient; // 使用传入的共享连接
        this.udpServer = udpServer;
        this.mqttHandler = mqttHandler;
        this.connected.set(true); // 使用共享连接，默认已连接
        this.udpInfo = new UdpInfo(sessionId, CryptoUtils.generateKey(16), this.generateUdpHeader(0, 0, 0));

        // 设备端 Topic
        this.publishTopic = String.format("devices/p2p/%s", clientId);

        // 注册连接ID映射
        mqttHandler.registerConnectionId(connectionId, sessionId);

        log.info("MQTT 会话创建成功，使用共享连接 - SessionId: {}, DeviceId: {}, Topic: {}, ConnectionId: {}",
                sessionId, deviceId, publishTopic, connectionId);
    }

    @Override
    public boolean isOpen() {
        return connected.get() && mqttClient.getState() == MqttClientState.CONNECTED;
    }

    @Override
    public void close() {
        if (!connected.get()) {
            return; // 已经关闭，避免重复操作
        }

        try {
            log.info("开始关闭MQTT会话 - SessionId: {}, DeviceId: {}, ConnectionId: {}", sessionId, deviceId, connectionId);

            // 设置连接状态为关闭
            connected.set(false);

            // 清理UDP连接信息
            if (udpInfo != null) {
                log.debug("清理UDP连接信息 - SessionId: {}", sessionId);
                udpInfo = null;
            }

            // 取消注册连接ID映射
            if (mqttHandler != null) {
                mqttHandler.unregisterConnectionId(connectionId);
            }

            log.info("MQTT 会话已关闭 - SessionId: {}, DeviceId: {}, ConnectionId: {} (使用共享连接，不断开)", sessionId, deviceId, connectionId);

        } catch (Exception e) {
            log.error("关闭MQTT会话时发生错误 - SessionId: {}", sessionId, e);
        }
    }

    @Override
    public void sendTextMessage(String message) {
        this.sendMqttMessage(message);
    }


    @Override
    public void sendBinaryMessage(byte[] message) {
        this.sendUdpMessage(message);
    }

    public boolean isConnected() {
        return connected.get();
    }

    /**
     * 发送 MQTT 消息
     *
     * @param message 消息内容
     */
    private void sendMqttMessage(String message) {
        if (!isOpen()) {
            log.warn("MQTT 连接未打开，无法发送文本消息 - SessionId: {}", sessionId);
            return;
        }

        log.info("MQTT 开始发送文本消息 {}", message);
        try {
            mqttClient.publishWith()
                    .topic(publishTopic)
                    .payload(message.getBytes(StandardCharsets.UTF_8))
                    .qos(MqttQos.AT_MOST_ONCE)
                    .retain(false)
                    .send()
                    .whenComplete((_, throwable) -> {
                        if (throwable != null) {
                            log.error("发送文本消息失败 - SessionId: {}, Message: {}", sessionId, message, throwable);
                        } else {
                            log.debug("发送文本消息到设备成功 - SessionId: {}, Topic: {}, Message: {}",
                                    sessionId, publishTopic, message);
                        }
                    });
        } catch (Exception e) {
            log.error("发送文本消息异常 - SessionId: {}, Message: {}", sessionId, message, e);
        }
    }

    /**
     * 发送 UDP 消息
     *
     * @param payload 音频数据
     */
    private void sendUdpMessage(byte[] payload) {
        if (this.udpInfo.getClientAddress() == null) {
            log.info("UDP client has been closed");
            return;
        }
        this.udpInfo.incrementLocalSequence();
        var timestamp = (int) System.currentTimeMillis() / 1000;
        var header = this.generateUdpHeader(payload.length, timestamp, this.udpInfo.getLocalSequence());

        CryptoUtils.encrypt(payload, udpInfo.getKey(), header)
                .map(encrypted -> ByteBuffer.allocate(header.length + encrypted.length)
                        .put(header)
                        .put(encrypted)
                        .array())
                .onSuccess(message -> this.udpServer.sendMessage(message, udpInfo.getClientAddress()))
                .onFailure(e -> log.error("加密UDP消息失败: {}", e.getMessage()));
    }

    /**
     * 生成 UDP 数据包的头部
     *
     * @param length    数据包的长度(不含头部)
     * @param timestamp 时间戳
     * @param sequence  序列号
     * @return Packet Header
     */
    private byte[] generateUdpHeader(int length, int timestamp, int sequence) {
        headerBuffer.clear();
        headerBuffer.order(ByteOrder.BIG_ENDIAN);
        headerBuffer.put((byte) 1);
        headerBuffer.put((byte) 0);
        headerBuffer.putShort((short) length);
        headerBuffer.putInt(connectionId);
        headerBuffer.putInt(timestamp);
        headerBuffer.putInt(sequence);

        return headerBuffer.array();
    }
}
